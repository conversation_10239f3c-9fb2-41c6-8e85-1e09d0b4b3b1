import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fuzzywuzzy/fuzzywuzzy.dart';
import 'package:snowball_stemmer/snowball_stemmer.dart';
import 'controllers/lesson_controller.dart';
import 'models/quiz_model.dart';
import 'widgets/box_widget.dart';
import 'widgets/customappbar.dart';
import 'widgets/optimized_merged_items_list.dart';
import 'widgets/premium_skeleton_loader.dart';
import 'utils/premium_animations.dart';

class Library extends StatefulWidget {
  final ScrollController scrollController;
  const Library({super.key, required this.scrollController});

  @override
  State<Library> createState() => _LibraryState();
}

class _LibraryState extends State<Library> with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  final searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  String searchText = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    searchController.removeListener(_onSearchChanged);
    searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Dismiss keyboard when returning to screen from another app/screen
      _searchFocusNode.unfocus();
    }
  }

  void _onSearchChanged() {
    setState(() {
      searchText = searchController.text.trim().toLowerCase();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss keyboard when tapping outside
        _searchFocusNode.unfocus();
      },
      child: Scaffold(
          backgroundColor: Colors.white,
          appBar: CustomAppBar(
            title: 'Izaberi',
            isBackButton: false,
          ),
          body: ScrollConfiguration(
            behavior: const ScrollBehavior(),
            child: Column(
              children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: PremiumAnimations.slideInFromBottom(
                  duration: const Duration(milliseconds: 600),
                  delay: const Duration(milliseconds: 200),
                  child: textFieldContainer(context,
                      controller: searchController,
                      focusNode: _searchFocusNode,
                      hint: 'Pretraga',
                      trailing: searchText.isNotEmpty
                          ? PremiumAnimations.scaleIn(
                              duration: const Duration(milliseconds: 300),
                              child: IconButton(
                                  onPressed: () {
                                    searchController.clear();
                                    _searchFocusNode.unfocus();
                                  },
                                  icon: const Icon(Icons.close)),
                            )
                          : null,
                      prefix: const Icon(Icons.search)),
                ),
              ),
              // Search results for lessons, quizzes, and shuffle quizzes
              Expanded(
                  child: searchText.isEmpty
                      ? OptimizedMergedItemsList(
                          isForLibrary: true,
                          scrollController: widget.scrollController,
                        )
                      : Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: _buildSearchResults(),
                        ))
              ],
            ),
          )),
    );
  }

  Widget _buildSearchResults() {
    return FutureBuilder(
      future: _fetchSearchResults(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: PremiumSkeletonLoader.list(itemCount: 6),
          );
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (!snapshot.hasData || (snapshot.data as List).isEmpty) {
          return Center(
            child: PremiumAnimations.fadeSlideIn(
              duration: const Duration(milliseconds: 600),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Nema rezultata pretrage',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Pokušajte sa drugim pojmom',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        List<dynamic> results = snapshot.data as List<dynamic>;
        return ListView.separated(
          separatorBuilder: (context, index) => const SizedBox(height: 20),
          controller: _scrollController,
          shrinkWrap: true,
          itemCount: results.length,
          itemBuilder: (context, index) {
            var item = results[index];
            
            return RepaintBoundary(
              key: ValueKey('search_item_$index'),
              child: PremiumAnimations.fadeSlideIn(
                duration: const Duration(milliseconds: 400),
                delay: Duration(milliseconds: index * 100),
                child: _buildSearchResultItem(item),
              ),
            );
          },
        );
      },
    );
  }

  Future<List<dynamic>> _fetchSearchResults() async {
    final stemmer = SnowballStemmer(Algorithm.serbian);
    final searchStem = stemmer.stem(searchText);
    final LessonController lessonController = Get.find();
    if (searchText.isEmpty) return [];

    // Create a list of items with their similarity scores
    List<MapEntry<dynamic, int>> itemsWithScores = [];

    for (var item in lessonController.allItems) {
      String itemName = '';
      if (item is LessonModel && item.lessonName != null) {
        itemName = item.lessonName!;
      } else if ((item is QuizModel || item is ShuffleQuizModel) &&
          item.quizName != null) {
        itemName = item.quizName!;
      }
      if (itemName.isNotEmpty) {
        final itemStem = stemmer.stem(itemName);
        if (searchStem == itemStem) {
          itemsWithScores.add(MapEntry(item, 100)); // Exact match
        } else {
          // Fallback to fuzzy matching on original forms with higher threshold
          int score = ratio(searchText.toLowerCase(), itemName.toLowerCase());
          int partialScore =
              partialRatio(searchText.toLowerCase(), itemName.toLowerCase());
          int tokenSortScore =
              tokenSortRatio(searchText.toLowerCase(), itemName.toLowerCase());
          int tokenSetScore =
              tokenSetRatio(searchText.toLowerCase(), itemName.toLowerCase());
          int finalScore = [score, partialScore, tokenSortScore, tokenSetScore]
              .reduce((a, b) => a > b ? a : b);
          if (finalScore >= 85) {
            // Increased threshold
            itemsWithScores.add(MapEntry(item, finalScore));
          }
        }
      }
    }
    // Sort by similarity score (highest first)
    itemsWithScores.sort((a, b) => b.value.compareTo(a.value));

    // Return the sorted items
    return itemsWithScores.map((entry) => entry.key).toList();
  }

  Widget _buildSearchResultItem(dynamic item) {
    if (item is LessonModel) {
      return BoxWidget(lesson: item);
    } else if (item is QuizModel) {
      return BoxWidget(quiz: item);
    } else if (item is ShuffleQuizModel) {
      return BoxWidget(shuffleQuiz: item);
    }
    return const SizedBox.shrink();
  }
}
