// ignore_for_file: unused_import

import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:bibl/controllers/analytics_controller.dart';
import 'package:bibl/controllers/payment_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/home.dart';
import 'package:bibl/leaderboard.dart';
import 'package:bibl/library.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/saved.dart';
import 'package:bibl/services/ad_mob_service.dart';
import 'package:bibl/services/ads_consent_service.dart';
import 'package:bibl/settings.dart' as settings;
import 'package:bibl/views/itemlink_landing_page.dart';
import 'package:bibl/widgets/info_sheet.dart';
import 'package:bibl/widgets/loading/home_loading_screen.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:rxdart/rxdart.dart';

import 'controllers/auth_controller.dart';
import 'controllers/index_controller.dart';
import 'controllers/category_controller.dart';
import 'controllers/heart_controller.dart';

import 'views/subscription/sub1.dart';
import 'widgets/interests_widget.dart';

class BNB extends StatefulWidget {
  const BNB({
    super.key,
  });

  @override
  State<BNB> createState() => _BNBState();
}

class _BNBState extends State<BNB> {
  late ScrollController homeScrollController;
  late ScrollController libraryScrollController;

  final AnalticsController analticsController = AnalticsController();
  final AdsConsentService adsConsentService = AdsConsentService();
  AppLinks appLinks = AppLinks();
  final AuthController authController = Get.find();
  final LessonController lessonController = Get.find();
  final IndexController indexController = Get.put(IndexController());
  final ProfileController profileController = Get.find<ProfileController>(); // Use existing permanent controller
  final CategoryController categoryController = Get.find();
  final HeartController heartController = Get.find();

  bool isDeviceConnected = false;

  StreamSubscription? subscription;
  bool isAlertSet = false;
  List<Widget> get _widgetOptions => (profileController
              .userr.value.isPremiumUser ??
          false)
      ? <Widget>[
          Home(scrollController: homeScrollController),
          Library(scrollController: libraryScrollController),
          const Saved(),
          const Leaderboard(),
          const settings.Settings(), // Only show Settings if user is premium
        ]
      : <Widget>[
          Home(scrollController: homeScrollController),
          Library(scrollController: libraryScrollController),
          const Saved(),
          const Leaderboard(),
          const SubscriptionScreen1(), // Show SubscriptionScreen1 for non-premium users
          const settings.Settings(),
        ];
  // Updated _bottomNavItems to accept isPremium as a parameter
  List<BottomNavigationBarItem> _bottomNavItems(bool isPremium) {
    return [
      _buildBottomNavItem('home_icon', 'Home', 0),
      _buildBottomNavItem('library_icon', 'Library', 1),
      _buildBottomNavItem('save_icon', 'Save', 2),
      _buildBottomNavItem('leaderboard_icon', 'Leaderboard', 3),
      if (!isPremium)
        _buildBottomNavItem('subscription_icon', 'Subscription', 4),
      _buildBottomNavItem('settings_icon', 'Settings', isPremium ? 4 : 5),
    ];
  }

  BottomNavigationBarItem _buildBottomNavItem(
      String icon, String label, int index) {
    return BottomNavigationBarItem(
      icon: indexController.selectedIndex.value == index
          ? Container(
              padding: const EdgeInsets.all(12.0),
              decoration: const BoxDecoration(
                color: Colors.white30,
                shape: BoxShape.circle,
              ),
              child: SvgPicture.asset('assets/svgs/$icon.svg'),
            )
          : Padding(
              padding: const EdgeInsets.only(bottom: 5),
              child: SvgPicture.asset('assets/svgs/$icon.svg'),
            ),
      label: label,
    );
  }

  getConnectivity() {
    if (!mounted) return;

    subscription = Connectivity()
        .onConnectivityChanged
        .debounceTime(const Duration(seconds: 3))
        .listen(
      (List<ConnectivityResult> results) async {
        // <-- Change yahan hua hai
        final isConnected = results.contains(ConnectivityResult.mobile) ||
            results.contains(ConnectivityResult.wifi);

        isDeviceConnected = await InternetConnectionChecker().hasConnection;

        if (!isConnected && !isDeviceConnected && !isAlertSet) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !isAlertSet) {
              showDialogBox();
              setState(() => isAlertSet = true);
            }
          });
        }
      },
    );
  }

  @override
  void initState() {
    super.initState();
    homeScrollController = ScrollController();
    libraryScrollController = ScrollController();

    _initDeepLinkListener();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await adsConsentService.initialize();
      PurchaseApi.premiumCheckerUpdate();
      getConnectivity();
    });
  }

  void _initDeepLinkListener() {
    appLinks.getInitialLink().then((deepLink) {
      _handleDeepLink(deepLink);
    });

    appLinks.uriLinkStream.listen((deepLink) {
      _handleDeepLink(deepLink);
    });
  }

  void _handleDeepLink(Uri? deepLink) async {
    // ProfileController profileController = Get.put(ProfileController());
    if (deepLink != null && authController.isThereDeepLink.value) {
      // Extract the type and id from query parameters
      String? type = deepLink.queryParameters.keys.first;
      String? itemId = deepLink.queryParameters[type];

      Get.to(() => ItemLinkLandingPage(
            itemId: itemId!,
            type: type,
          ));
    } else {}
  }

  void _scrollToTop(ScrollController controller) {
    if (controller.hasClients) {
      controller.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    homeScrollController.dispose();
    libraryScrollController.dispose();
    subscription?.cancel();
    subscription = null;

    super.dispose();
  }

  void _onItemTapped(int index) {
    // Dismiss any active keyboard when switching tabs
    FocusScope.of(context).unfocus();
    
    // Check if the user is re-selecting the Home or Library tab
    if (indexController.selectedIndex.value == index) {
      if (index == 0) {
        // Home tab clicked again
        _scrollToTop(homeScrollController);
      } else if (index == 1) {
        // Library tab clicked again
        _scrollToTop(libraryScrollController);
      }
    }

    // Update the selected index
    setState(() {
      indexController.selectedIndex.value = index;
    });
  }

  showDialogBox() {
    GetPlatform.isAndroid
        ? showDialog<String>(
            context: context,
            builder: (BuildContext context) => CupertinoAlertDialog(
              title: const Text('Nema veze'),
              content:
                  const Text('Molimo vas da proverite svoju internet vezu.'),
              actions: <Widget>[
                TextButton(
                  onPressed: () async {
                    Navigator.pop(context, 'Prekini');
                    setState(() => isAlertSet = false);
                    isDeviceConnected =
                        await InternetConnectionChecker().hasConnection;
                    if (!isDeviceConnected && isAlertSet == false) {
                      showDialogBox();
                      setState(() => isAlertSet = true);
                    }
                  },
                  child: const Text('U redu'),
                ),
              ],
            ),
          )
        : showCupertinoDialog<String>(
            context: context,
            builder: (BuildContext context) => CupertinoAlertDialog(
              title: const Text('Nema veze'),
              content:
                  const Text('Molimo vas da proverite svoju internet vezu.'),
              actions: <Widget>[
                TextButton(
                  onPressed: () async {
                    Navigator.pop(context, 'Prekini');
                    setState(() => isAlertSet = false);
                    isDeviceConnected =
                        await InternetConnectionChecker().hasConnection;
                    if (!isDeviceConnected && isAlertSet == false) {
                      showDialogBox();
                      setState(() => isAlertSet = true);
                    }
                  },
                  child: const Text('U redu'),
                ),
              ],
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Show loading screen while data is being loaded
      if (profileController.isUserDataLoading.value) {
        return const PopScope(
          canPop: false,
          child: HomeLoadingScreen(),
        );
      }

      return PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, Object? result) async {
          if (!didPop) {
            // Exit the app when back button is pressed on bottom navigation screens
            SystemNavigator.pop();
          }
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Obx(() => IndexedStack(
                index: indexController.selectedIndex.value,
                children: _widgetOptions,
              )),
          bottomNavigationBar: Obx(() {
            bool isPremium =
                profileController.userr.value.isPremiumUser ?? false;
            return Container(
              decoration: BoxDecoration(
                gradient: mainColorsGradient,
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20)),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(0, -6),
                    blurRadius: 4,
                    spreadRadius: 0,
                    color: pinkColor.withValues(alpha: 0.25),
                  ),
                ],
              ),
              child: BottomNavigationBar(
                backgroundColor: Colors.transparent,
                showSelectedLabels: false,
                showUnselectedLabels: false,
                items: _bottomNavItems(isPremium),
                type: BottomNavigationBarType.fixed,
                currentIndex: indexController.selectedIndex.value,
                selectedFontSize: 10,
                unselectedFontSize: 10,
                selectedItemColor: mainColor,
                unselectedItemColor: greyishColor,
                onTap: _onItemTapped,
                elevation: 0,
              ),
            );
          }),
        ),
      );
    });
  }
}
